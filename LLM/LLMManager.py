from typing import Dict

from LLM.BaseLLMHelper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from LLM.EmbeddingsManager import EmbeddingsManager
from LLM.OllamaLLMHelper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from LLM.OpenAiLLMHelper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>per
from Models.peewee.OrmModel import KBModel, ModelsPlatformName
from Services.SqlServer.KBModelsService import KBModelsService


class LLMManager:
    _llm_map: Dict[int, BaseLLMHelper] = {}

    @staticmethod
    def __create_llm_helper(*, id: int = None, model: KBModel = None) -> BaseLLMHelper | None:
        if id:
            model = KBModel.get_by_id(id)
        if model.platform_type == ModelsPlatformName.OLLAMA.value:
            return OllamaLLMHelper(model)
        elif model.platform_type == ModelsPlatformName.OPENAI.value:
            return OpenAiLLMHelper(model)
        elif model.platform_type == ModelsPlatformName.DEEPSEEK.value:
            return <PERSON>Ai<PERSON><PERSON><PERSON><PERSON>per(model)
        elif model.platform_type == ModelsPlatformName.SPARK.value:
            return OpenAi<PERSON><PERSON><PERSON>elper(model)
        elif model.platform_type == ModelsPlatformName.QWEN.value:
            return OpenAiLLMHelper(model)
        return None

    def get_llm_helper(self, *, id: int = None, model: KBModel = None) -> BaseLLMHelper | None:
        if model:
            id = model.id

        llm_helper = self._llm_map.get(id, None)

        if llm_helper is None:
            llm_helper = self.__create_llm_helper(id=id, model=model)
            if llm_helper is not None:
                self._llm_map[id] = llm_helper
        return llm_helper

    def get_chat_use_llm_helper(self) -> BaseLLMHelper | None:
        return self.get_llm_helper(model=KBModelsService.get_default_chat_model())

    def get_generate_use_llm_helper(self) -> BaseLLMHelper | None:
        return self.get_llm_helper(model=KBModelsService.get_generate_model())


sys_llm_manager = LLMManager()
embeddings_manager = EmbeddingsManager()
