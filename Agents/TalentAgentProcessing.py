from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import SystemMessagePromptTemplate, HumanMessagePromptTemplate, \
    ChatPromptTemplate
from langchain_core.runnables import <PERSON>nableLambda
from langchain_core.runnables.history import RunnableWithMessageHistory
import re
from Configs.Config import SysConfig
from LLM.LLMManager import sys_llm_manager
from Models.agent.ResumeInfo import ResumeInfo
from Models.agent.TalentInfoProcessing import *
from Utils.BossAnalysisUtils import ResumeSplitter
from Utils.CommonUtils import remove_think_tags
from Utils.DeadlockMonitor import TaskMonitorContext
from Utils.logs.LoggingConfig import logger

# 基本信息
base_system_parser = PydanticOutputParser(pydantic_object=TalentAgentProcessing)
base_system_condition = ("准确识别并按照指定格式填充内容，例如姓名、性别、年龄、学历、邮箱、手机号码、现居地址、"
                         "政治面貌、专业水平、应聘职位(期望的职位、期望的岗位名称)、"
                         "期望薪资、工作经验(只获取年份例如：'4年9个月' 取值后 只取年份 4)、期望薪资下限、期望薪资上限等数据。")

# 个人经历
personal_system_parser = PydanticOutputParser(pydantic_object=Undergo)
personal_system_condition = ("准确识别并按照指定格式填充内容，例如最高学历、外语水平、专业水平、个人证书或资质(使用 ',' 拼接)、"
                             "教育经历列表(字段名称：tbEducationInfoList，信息：学历、学校名称、专业、起始日期、截至日期)、"
                             "工作经历列表(字段名称：tbWorkExperienceList，信息：入职过的公司名称、职位、在职时工作的内容、起始年月、截至年月)。")

# 项目经验
project_parser = PydanticOutputParser(pydantic_object=ProjectExperienceAgentProcessing)
project_condition = ("准确识别内容，例如技能列表和项目信息(项目名称、在项目中担任职位、项目描述、职责内容、项目起始日期、项目截至日期)。"
                     "如果没有明确指出项目经历信息，直接返回不在项目经历进行解析了")

# 统一提示词模板
system_template = SystemMessagePromptTemplate.from_template(
    """你是一个专业人事经理，根据用户给出的内容进行提取出相关信息，并严格按照规定格式进行输出。
    转化规则:
        1. 仔细阅读并识别用户提供的文本内容，严格按照字段名称和字段含义来赋值绝对不能填充内容以外的信息。
        2. {condition}
    注意事项:
        1. 确保输出格式中的信息是从给定内容中提取的。
        2. 输出格式必须严格按照规定的格式进行输出,字段驼峰命名绝对不允许变动。
        3. 如果内容中没有对应的信息可以用 "" 或 null。
        4. 输出的信息必须是标准的有效的JSON格式，一定不能包含任何额外的字符或空格。
        5. 对于日期等相关数据按照格式转换为 YYYY-MM 格式,例如 "2025.03" 转换后 "2025-03"。
        6. 对于薪资范围，按照格式转换为 K 格式，例如 "10000-20000" 转换后 "10-20"，"6千-1万" 转换后 6-10。
    输出格式：{format_instructions}
    /no_think
   """
)

score_system_template = PydanticOutputParser(pydantic_object=TalentScore)
# 对指定字段进行评分的提示词模板
score_template = SystemMessagePromptTemplate.from_template(
    """你是一位在人力资源领域拥有多年经验的资深专家，对各类企业和行业的招聘需求有着敏锐的洞察力。
    你熟悉各类评分标准和评价体系，能够客观、公正地对简历进行分析评价，并提供具有建设性的反馈。
    评分、评价规则:
        1. 根据内容进行分析并对工作经验、教育经历、期望薪资和任职频率(工作经历)四项内容进行评分评价。
        2. 如果附加条件存在内容则需要结合附加条件给出一个匹配分(满分100分)。
        3. 结合内容对其进行一个整体的综合评价。
        4. 评价内容中不能带有评分分值相关字段。
    注意事项:
        - 评分的满分是 100 分, 根据其指内容信息进行打分，绝对不能超过满分。
        - 输出的信息必须是标准的有效的JSON格式，一定不能包含任何额外的字符或空格。
    附加条件：{condition}
    输出格式：{format_instructions}
    /no_think
   """
)


class TalentAgentProcessing:
    _llm_name: str | None = "DEFAULT"

    def __init__(self):
        self.__temperature = SysConfig["agents"]["kb_agent"]["temperature"]
        self.__talent_url = SysConfig["talent"]["talent_url"]
        self.__tokens = SysConfig["talent"]["tokens"]

    def __set_llm(self, out: PydanticOutputParser,
                  template: SystemMessagePromptTemplate, condition: str = None) -> RunnableWithMessageHistory | None:
        llm_helper = sys_llm_manager.get_generate_use_llm_helper()
        if llm_helper is None:
            return None
        llm_obj = llm_helper.get_llm_object(self.__temperature, self.__tokens)
        human_template = HumanMessagePromptTemplate.from_template(
            "内容：{content}"
        )

        prompt_template = ChatPromptTemplate.from_messages([
            template,
            human_template
        ]).partial(format_instructions=out.get_format_instructions(), condition=condition)

        runnable = (prompt_template | llm_obj
                    | RunnableLambda(lambda x: x if isinstance(x, str) else x.content)
                    | RunnableLambda(remove_think_tags)
                    | out)
        return runnable

    def _formatting(self, content: str, condition: str = None, boss_flag: bool = False) -> Optional[ResumeInfo]:
        """
        格式化简历内容，提取各部分信息并组装成完整的简历对象

        Args:
            content: 原始简历文本内容
            condition: 评分条件

        Returns:
            处理后的简历信息对象或None
        """
        try:
            content = self.clean_resume_text(content)
            if boss_flag :
                result = self.intelligent_resume_segmentation(content)
            else:
                result = ContentTrunk()
                result.basicInfo = content
                result.workExperience = content
                result.educationExperience = content
                result.projectExperience = content

            # 智能分块处理
            print(result)
            # 并行处理各部分信息
            talent_info = self._extract_basic_info(result)
            print(talent_info)
            personal_info = self._extract_personal_info(result, boss_flag)
            print(personal_info)
            project_experience = self._extract_project_experience(result)

            # 评分处理
            logger.info("开始处理最终评分信息")
            score_info = self._calculate_resume_score(content, condition)
            logger.info("结束处理最终评分信息")

            return self._assemble_resume_object(talent_info, personal_info,
                                                project_experience, score_info)
        except Exception as e:
            logger.error(f"简历格式化处理失败: {str(e)}")
            return None

    # 个人经历信息
    def _extract_personal_info(self, result: ContentTrunk, boss_flag: bool = False) -> Optional[Undergo]:
        """
        提取个人经历信息
        Args:
            result: 分块后的简历内容
        Returns:
            个人经历信息对象或None
        """
        if not result.basicInfo:
            return None
        logger.info("开始处理个人经历信息")
        try:
            if boss_flag:
                content = (result.educationExperience+result.workExperience)
            else:
                content = result.basicInfo
            with TaskMonitorContext("extract_personal_info", timeout_threshold=120.0):
                personal_info = self.__set_llm(personal_system_parser, system_template, personal_system_condition).invoke(
                    {"content": content}
                )
            logger.info("个人经历信息处理完成")
            return personal_info
        except Exception as e:
            logger.error(f"个人经历信息处理失败: {str(e)}")
            return None

    def _extract_basic_info(self, result: ContentTrunk) -> Optional[TalentAgentProcessing]:
        """
        提取基本个人信息

        Args:
            result: 分块后的简历内容

        Returns:
            基本信息对象或None
        """
        if not result.basicInfo:
            return None

        logger.info("开始处理个人基本信息")
        try:
            with TaskMonitorContext("extract_basic_info", timeout_threshold=120.0):
                talent_info = self.__set_llm(base_system_parser, system_template, base_system_condition).invoke(
                    {"content": result.basicInfo}
                )
            logger.info("个人基本信息处理完成")
            return talent_info
        except Exception as e:
            logger.error(f"个人基本信息处理失败: {str(e)}")
            return None

    def _extract_project_experience(self, result: ContentTrunk) -> Optional[ProjectExperienceAgentProcessing]:
        """
        提取项目经历信息

        Args:
            result: 分块后的简历内容

        Returns:
            项目经历信息对象或None
        """
        if not result.projectExperience:
            return None

        logger.info("开始处理项目经历信息")
        try:
            with TaskMonitorContext("extract_project_experience", timeout_threshold=120.0):
                project_experience = self.__set_llm(project_parser, system_template, project_condition).invoke(
                    {"content": result.projectExperience}
                )
            logger.info("项目经历信息处理完成")
            return project_experience
        except Exception as e:
            logger.error(f"项目经历信息处理失败: {str(e)}")
            return None

    def _calculate_resume_score(self, content: str, condition: str = None) -> Optional[TalentScore]:
        try:
            with TaskMonitorContext("calculate_resume_score", timeout_threshold=120.0):
                score_info = self.__set_llm(score_system_template, score_template, condition).invoke(
                    {"content": content}
                )
            return score_info
        except Exception as e:
            logger.error(f"简历评分计算失败: {str(e)}")
            return None

    def _assemble_resume_object(self, personal_info: Optional[TalentAgentProcessing] = None,
                                personal_experience: Optional[Undergo] = None,
                                project_experience: Optional[ProjectExperienceAgentProcessing] = None,
                                score_info: Optional[TalentScore] = None) -> ResumeInfo:
        talent_info = ResumeInfo()

        # 填充个人基本信息
        if personal_info:
            self._fill_personal_info(talent_info, personal_info)

        # 经历信息
        if personal_experience:
            talent_info.education = personal_experience.education
            talent_info.professionalLevel = personal_experience.professionalLevel
            talent_info.foreignProficiency = personal_experience.foreignProficiency
            talent_info.certificate = personal_experience.certificate
            talent_info.schoolName = personal_experience.schoolName
            talent_info.major = personal_experience.major
            # 处理集合中重复的数据
            talent_info.tbEducationInfoList = self._remove_duplicates(personal_experience.tbEducationInfoList,key_func=lambda x:(x.graduationSchool,x.major))
            talent_info.tbWorkExperienceList = self._remove_duplicates(personal_experience.tbWorkExperienceList,key_func=lambda x:x.companyName)

        # 填充项目经历信息
        if project_experience:
            talent_info.skillList = project_experience.skillList
            talent_info.tbProjectExperienceList = project_experience.tbProjectExperienceList


        # 填充评分信息
        if score_info:
            self._fill_score_info(talent_info, score_info)


        return talent_info

    def _fill_personal_info(self, talent_info: ResumeInfo, personal_info: TalentAgentProcessing) -> None:
        """填充个人基本信息"""
        talent_info.userName = personal_info.userName
        talent_info.sex = personal_info.sex
        talent_info.age = personal_info.age
        talent_info.email = personal_info.email
        talent_info.phone = personal_info.phone
        talent_info.yearsOfExperience = personal_info.yearsOfExperience
        talent_info.ethnicity = personal_info.ethnicity
        talent_info.marriageStatus = personal_info.marriageStatus
        talent_info.politicalStatus = personal_info.politicalStatus
        talent_info.currentAddress = personal_info.currentAddress
        talent_info.introduction = personal_info.introduction
        talent_info.jobIntent = personal_info.jobIntent
        talent_info.salaryExpectation = personal_info.salaryExpectation
        talent_info.workStatus = personal_info.workStatus
        talent_info.salaryRangeLowerBound = personal_info.salaryRangeLowerBound
        talent_info.salaryRangeUpperBound = personal_info.salaryRangeUpperBound


    def _fill_score_info(self, talent_info: ResumeInfo, score_info: TalentScore) -> None:
        """填充综合评分信息"""
        talent_info.minimumEducationScore = score_info.minimumEducationScore
        talent_info.workExperienceScore = score_info.workExperienceScore
        talent_info.jobHoppingRateScore = score_info.jobHoppingRateScore
        talent_info.salaryRangeScore = score_info.salaryRangeScore
        talent_info.comprehensiveEvaluation = score_info.totalEvaluate
        talent_info.educationEvaluation = score_info.educationEvaluate
        talent_info.workExperienceEvaluation = score_info.workExperienceEvaluate
        talent_info.jobHoppingRateEvaluation = score_info.jobHoppingRateEvaluate
        talent_info.salaryRangeEvaluation = score_info.salaryRangeEvaluate
        talent_info.totalScore = score_info.totalScore
        talent_info.matchScore = score_info.matchScore

    def intelligent_resume_segmentation(self, text: str) -> ContentTrunk:
        logger.info("开始智能简历内容分块")
        try:
            logger.info("准备使用简单分块..")
            result = ResumeSplitter.split_resume(text)
            # else:
                # 使用LLM进行分块（较慢但更准确）
                # logger.info("准备使用LLM进行分块..")
                # result = self._llm_based_segmentation(text)
            return result
        except Exception as e:
            logger.error(f"分块失败: {str(e)}")

    def _llm_based_segmentation(self, text: str) -> ContentTrunk:
        """使用LLM进行智能分块"""
        logger.info("开始使用LLM进行分块")
        parser = PydanticOutputParser(pydantic_object=ContentTrunk)
        template = SystemMessagePromptTemplate.from_template(
            """你是一位专业的简历结构分析专家，能够准确识别简历中的信息进行区分，并严格按照规定格式进行输出。
                转化规则:
                    - 如果没有相关信息，则对应字段可输出为null 或者 "",。
                    - 仔细阅读并识别用户提供的文本内容，严格按照原文给的信息区分出基础信息、工作经历、项目经历、教育经历这四大内容文本，内容块中是纯文本，不要输出成json。
                    - 基础信息内容：姓名、性别、年龄、学历、邮箱、手机号码、现居地址、政治面貌、外语水平、专业水平、求职意向(期望职位 例如 '青岛|Python'、'杭州|律师' 等)、期望薪资、工作经验、期望薪资下限、期望薪资上限等数据。
                    - 工作经历内容：公司名称、职位、工作描述、起始年份、截至年份。
                    - 教育经历内容：学校名称、专业、学历信息、证书信息。
                    - 项目经历内容：技能列表、项目名称、在项目担任职位、职责内容、项目起始日期、项目截至日期。
                输出格式：{format_instructions}
                /no_think
                """
        )
        info = self.__set_llm(parser, template).invoke(
            {"content": text}
        )
        logger.info(f"内容分块完成，结果为：{info}")
        return info

    def clean_resume_text(self, text):
        text = re.sub('edf753e2f0df43991XZ729i9FltRwo65WP6WWOGim_PTPxZj2A~~', '', text)
        # 删除连续的空格，只保留一个空格
        text = re.sub(r' +', ' ', text)
        # 删除连续的换行符，只保留一个换行
        text = re.sub(r'\n+', '\n', text)
        # 删除行首和行尾的空格
        text = '\n'.join([line.strip() for line in text.split('\n')])
        # 删除空行
        text = '\n'.join([line for line in text.split('\n') if line.strip() != ''])
        return text

    def _remove_duplicates(self,object_list, key_func=None):
        """去除列表中重复的对象"""
        seen = set()
        unique_list = []
        for obj in object_list:
            if key_func:
                key = key_func(obj)
            else:
                key = str(obj)  # 默认使用字符串表示作为键

            if key not in seen:
                seen.add(key)
                unique_list.append(obj)
        return unique_list